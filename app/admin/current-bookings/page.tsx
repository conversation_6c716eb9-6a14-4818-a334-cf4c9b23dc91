'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { PageTransition } from '@/components/ui/page-transition';
import { useAdmin, withAdminAuth } from '@/contexts/AdminContext';
import { CalendarCheck, ChevronLeft, ChevronRight, ArrowLeft, CheckCircle } from 'lucide-react';
import { motion } from 'framer-motion';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';

interface CurrentBooking {
  id: number;
  admission_number: string;
  student_name: string;
  bus_route: string;
  destination: string;
  payment_status: boolean;
  created_at: string;
  fare: number | null;
  boarded: boolean;
}

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

function CurrentBookingsPage() {
  const { user } = useAdmin();
  const router = useRouter();
  const [bookings, setBookings] = useState<CurrentBooking[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 1
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchCurrentBookings();
  }, [pagination.page]);

  const fetchCurrentBookings = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/admin/current-bookings?page=${pagination.page}&limit=${pagination.limit}`, {
        credentials: 'include'
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setBookings(result.data.bookings);
          setPagination(prev => ({
            ...prev,
            ...result.data.pagination
          }));
        } else {
          toast.error('Failed to fetch current bookings');
        }
      } else {
        toast.error('Failed to fetch current bookings');
      }
    } catch (error) {
      console.error('Failed to fetch current bookings:', error);
      toast.error('Failed to fetch current bookings');
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number | null) => {
    if (amount === null || amount === undefined) return '₹0.00';
    return `₹${amount.toFixed(2)}`;
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading current bookings...</p>
        </div>
      </div>
    );
  }

  return (
    <PageTransition>
      <div className="min-h-screen p-4 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="flex justify-between items-center mb-8"
          >
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={() => router.push('/admin/dashboard')}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Dashboard
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-gray-800 mb-2 flex items-center gap-2">
                  <CalendarCheck className="w-8 h-8" />
                  Current Bookings
                </h1>
                <p className="text-gray-600">Active bookings for the current travel period</p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-blue-600">{pagination.total}</div>
              <div className="text-sm text-gray-600">Total Current Bookings</div>
            </div>
          </motion.div>

          {/* Current Bookings Table */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CalendarCheck className="w-5 h-5" />
                  Current Bookings ({pagination.total} total)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Admission Number</TableHead>
                        <TableHead>Date and Time Booked</TableHead>
                        <TableHead>Route Name</TableHead>
                        <TableHead>Destination</TableHead>
                        <TableHead>Fare</TableHead>
                        <TableHead>Payment Status</TableHead>
                        <TableHead>Boarding Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {bookings.map((booking) => (
                        <TableRow
                          key={booking.id}
                          className={booking.boarded ? 'bg-green-50 border-green-200' : ''}
                        >
                          <TableCell className="font-medium">{booking.student_name}</TableCell>
                          <TableCell>{booking.admission_number}</TableCell>
                          <TableCell>{formatDateTime(booking.created_at)}</TableCell>
                          <TableCell>
                            <Badge variant="outline">{booking.bus_route}</Badge>
                          </TableCell>
                          <TableCell>{booking.destination}</TableCell>
                          <TableCell className="font-medium">
                            {formatCurrency(booking.fare)}
                          </TableCell>
                          <TableCell>
                            <Badge variant={booking.payment_status ? "default" : "secondary"}>
                              {booking.payment_status ? "Paid" : "Pending"}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {booking.boarded ? (
                              <Badge variant="default" className="bg-green-600 hover:bg-green-700 text-white">
                                <CheckCircle className="w-3 h-3 mr-1" />
                                Boarded
                              </Badge>
                            ) : (
                              <Badge variant="secondary" className="text-slate-600">
                                Not Boarded
                              </Badge>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
                
                {/* Pagination */}
                {pagination.totalPages > 1 && (
                  <div className="flex items-center justify-between space-x-2 py-4">
                    <div className="text-sm text-gray-500">
                      Page {pagination.page} of {pagination.totalPages} 
                      ({pagination.total} total current bookings)
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(Math.max(1, pagination.page - 1))}
                        disabled={pagination.page === 1}
                      >
                        <ChevronLeft className="w-4 h-4" />
                        Previous
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(Math.min(pagination.totalPages, pagination.page + 1))}
                        disabled={pagination.page === pagination.totalPages}
                      >
                        Next
                        <ChevronRight className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                )}

                {bookings.length === 0 && (
                  <div className="text-center py-8">
                    <CalendarCheck className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                    <p className="text-gray-500">No current bookings found</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </PageTransition>
  );
}

export default withAdminAuth(CurrentBookingsPage);

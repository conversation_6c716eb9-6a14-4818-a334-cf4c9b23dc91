'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { PageTransition } from '@/components/ui/page-transition';
import { withAdminAuth } from '@/contexts/AdminContext';
import { Users, Plus, Edit, Trash2, Save, X, ArrowLeft, Search, ChevronUp, ChevronDown, ChevronLeft, ChevronRight } from 'lucide-react';
import { motion } from 'framer-motion';
import { toast } from 'sonner';
import Link from 'next/link';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import StudentModal from '@/components/StudentModal'; // Import the new modal component

interface Student {
  id: string;
  name: string;
  admission_number: string;
  email: string | null;
  hostel: string | null;
  route: string | null;
  created_at: string;
  updated_at: string;
}

interface StudentFormData {
  name: string;
  admission_number: string;
  email: string | null;
  hostel: string | null;
  route: string | null;
}

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}
type SortField = 'name' | 'admission_number' | 'hostel' | 'route' | 'created_at';
type SortOrder = 'asc' | 'desc';

const hostelOptions = ['St Mary\'s Hostel', 'St Alphonsa Hostel','St Augustine Hostel','St Thomas Hostel'];

interface RouteOption {
  route_code: string;
  bus_name: string;
}

function StudentManagement() {
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<SortField>('created_at');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [routeOptions, setRouteOptions] = useState<RouteOption[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0
  });
  
  // Form states
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingStudent, setEditingStudent] = useState<Student | null>(null);
  const [formLoading, setFormLoading] = useState(false);

  // New state for modal
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Fetch students
  const fetchStudents = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        sortBy: sortField,
        sortOrder: sortOrder,
        ...(searchTerm && { search: searchTerm })
      });

      const response = await fetch(`/api/admin/students?${params}`, {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to fetch students');
      }

      const result = await response.json();
      if (result.success) {
        setStudents(result.data.students);
        setPagination(result.data.pagination);
      } else {
        throw new Error(result.error || 'Failed to fetch students');
      }
    } catch (error) {
      console.error('Error fetching students:', error);
      toast.error('Failed to fetch students');
    } finally {
      setLoading(false);
    }
  };

  // Fetch routes from buses table
  const fetchRoutes = async () => {
    try {
      const response = await fetch('/api/admin/buses', {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to fetch routes');
      }

      const result = await response.json();
      if (result.success) {
        // Extract unique route codes with bus names from buses data
        const routesMap = new Map();
        
        result.data
          .filter((bus: any) => bus.is_active) // Only active buses
          .forEach((bus: any) => {
            if (!routesMap.has(bus.route_code)) {
              routesMap.set(bus.route_code, {
                route_code: bus.route_code,
                bus_name: bus.name
              });
            }
          });

        const routes = Array.from(routesMap.values()).sort((a: RouteOption, b: RouteOption) => {
          // Sort by route number (route-1, route-2, etc.)
          const getRouteNumber = (route: string) => {
            const match = route.match(/route-(\d+)/);
            return match ? parseInt(match[1], 10) : 0;
          };
          return getRouteNumber(a.route_code) - getRouteNumber(b.route_code);
        });
        
        setRouteOptions(routes);
      } else {
        console.error('Failed to fetch routes:', result.error);
        setRouteOptions([]);
      }
    } catch (error) {
      console.error('Error fetching routes:', error);
      setRouteOptions([]);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchRoutes(); // Fetch routes first
    fetchStudents();
  }, [pagination.page, sortField, sortOrder, searchTerm]);

  // Handle search with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      if (pagination.page !== 1) {
        setPagination(prev => ({ ...prev, page: 1 }));
      } else {
        fetchStudents();
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Handle sorting
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('asc');
    }
  };

  // Handle form submission (now used by modal)
  const handleStudentSubmit = async (studentData: StudentFormData) => {
    setFormLoading(true);

    try {
      const url = '/api/admin/students';
      const method = editingStudent ? 'PUT' : 'POST';
      const body = editingStudent 
        ? { ...studentData, id: editingStudent.id }
        : studentData;

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
        credentials: 'include'
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success(editingStudent ? 'Student updated successfully' : 'Student added successfully');
        setIsModalOpen(false);
        setEditingStudent(null);
        fetchStudents();
      } else {
        throw new Error(result.error || 'Operation failed');
      }
    } catch (error) {
      console.error('Error saving student:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to save student');
    } finally {
      setFormLoading(false);
    }
  };

  // Handle delete student
  const handleDeleteStudent = async (student: Student) => {
    if (!confirm(`Are you sure you want to delete ${student.name}? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/students?id=${student.id}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success('Student deleted successfully');
        fetchStudents();
      } else {
        throw new Error(result.error || 'Failed to delete student');
      }
    } catch (error) {
      console.error('Error deleting student:', error);
      toast.error('Failed to delete student');
    }
  };

  // Render sort icon
  const renderSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return <ChevronUp className="w-4 h-4 opacity-30" />;
    }
    return sortOrder === 'asc' ? 
      <ChevronUp className="w-4 h-4" /> : 
      <ChevronDown className="w-4 h-4" />;
  };

  function handlePageChange(page: number): void {
    if (page < 1 || page > pagination.totalPages || page === pagination.page) return;
    setPagination(prev => ({ ...prev, page }));
  }
  return (
    <PageTransition>
      <div className="min-h-screen p-3 md:p-4 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          {/* Header - Mobile optimized */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 md:gap-0 mb-6 md:mb-8"
          >
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 w-full md:w-auto">
              <Link href="/admin/dashboard" className="w-full sm:w-auto">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full sm:w-auto min-h-[44px] text-base md:text-sm px-3 py-2"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Dashboard
                </Button>
              </Link>
              <div>
                <h1 className="text-2xl md:text-3xl font-bold text-gray-800 mb-1 md:mb-2">Student Management</h1>
                <p className="text-gray-600 text-sm md:text-base">Manage student information and records</p>
              </div>
            </div>
            <Button
              onClick={() => {
                setEditingStudent(null);
                setIsModalOpen(true);
              }}
              className="w-full sm:w-auto mt-2 md:mt-0 bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 min-h-[44px]"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Student
            </Button>
          </motion.div>

          {/* Search and Filters - Mobile optimized */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-6"
          >
            <Card>
              <CardContent className="p-4 md:pt-6">
                <div className="flex flex-col md:flex-row items-start md:items-center gap-3 md:gap-4">
                  <div className="relative w-full md:max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="Search students..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 min-h-[44px]"
                    />
                  </div>
                  <div className="text-sm text-gray-600 w-full md:w-auto text-center md:text-left">
                    {pagination.total} student{pagination.total !== 1 ? 's' : ''} found
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Students Table - Mobile optimized with horizontal scrolling */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card>
              <CardHeader className="p-4 md:p-6">
                <CardTitle className="flex items-center gap-2 text-lg md:text-xl">
                  <Users className="w-5 h-5" />
                  Students List
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0 pb-4 md:p-6 md:pt-0">
                {loading ? (
                  <div className="flex items-center justify-center py-12">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
                    <span className="ml-2">Loading students...</span>
                  </div>
                ) : students.length === 0 ? (
                  <div className="text-center py-12">
                    <Users className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-semibold text-gray-600 mb-2">No Students Found</h3>
                    <p className="text-gray-500 mb-4 px-4">
                      {searchTerm ? 'No students match your search criteria.' : 'Get started by adding your first student.'}
                    </p>
                    {!searchTerm && (
                      <Button
                        onClick={() => {
                          setEditingStudent(null);
                          setIsModalOpen(true);
                        }}
                        className="bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 min-h-[44px]"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Add First Student
                      </Button>
                    )}
                  </div>
                ) : (
                  <>
                    {/* Mobile Card View (< 768px) */}
                    <div className="block md:hidden space-y-3 px-4 mt-4">
                      {students.map((student) => (
                        <div 
                          key={student.id}
                          className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
                        >
                          <div className="flex justify-between items-start mb-2">
                            <h3 className="font-medium text-gray-900 text-base">{student.name}</h3>
                            <Badge variant="outline" className="font-mono text-xs">
                              {student.admission_number}
                            </Badge>
                          </div>

                          <div className="space-y-2 text-sm mb-3">
                            <div className="flex justify-between items-center">
                              <span className="text-gray-500">Email:</span>
                              <span className="text-gray-800 max-w-[180px] truncate text-right">
                                {student.email || '-'}
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-gray-500">Hostel:</span>
                              <span className="text-gray-800">{student.hostel || '-'}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-gray-500">Route:</span>
                              <span>
                                {student.route ? (
                                  <Badge variant="secondary" className="text-xs">{student.route}</Badge>
                                ) : (
                                  '-'
                                )}
                              </span>
                            </div>
                          </div>

                          <div className="flex gap-2 pt-2 border-t border-gray-100">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                setEditingStudent(student);
                                setIsModalOpen(true);
                              }}
                              className="flex-1 min-h-[40px]"
                            >
                              <Edit className="w-4 h-4 mr-2" />
                              Edit
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="flex-1 text-red-600 border-red-200 hover:bg-red-50 min-h-[40px]"
                              onClick={() => handleDeleteStudent(student)}
                            >
                              <Trash2 className="w-4 h-4 mr-2" />
                              Delete
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Desktop Table View (≥ 768px) */}
                    <div className="hidden md:block overflow-x-auto">
                      <Table className="min-w-full">
                        <TableHeader>
                          <TableRow>
                            <TableHead
                              className="cursor-pointer hover:bg-gray-50 select-none"
                              onClick={() => handleSort('name')}
                            >
                              <div className="flex items-center gap-1">
                                Name
                                {renderSortIcon('name')}
                              </div>
                            </TableHead>
                            <TableHead
                              className="cursor-pointer hover:bg-gray-50 select-none"
                              onClick={() => handleSort('admission_number')}
                            >
                              <div className="flex items-center gap-1">
                                Admission Number
                                {renderSortIcon('admission_number')}
                              </div>
                            </TableHead>
                            <TableHead className="hidden md:table-cell">Email</TableHead>
                            <TableHead
                              className="hidden md:table-cell cursor-pointer hover:bg-gray-50 select-none"
                              onClick={() => handleSort('hostel')}
                            >
                              <div className="flex items-center gap-1">
                                Hostel
                                {renderSortIcon('hostel')}
                              </div>
                            </TableHead>
                            <TableHead
                              className="hidden md:table-cell cursor-pointer hover:bg-gray-50 select-none"
                              onClick={() => handleSort('route')}
                            >
                              <div className="flex items-center gap-1">
                                Route
                                {renderSortIcon('route')}
                              </div>
                            </TableHead>
                            <TableHead
                              className="hidden lg:table-cell cursor-pointer hover:bg-gray-50 select-none"
                              onClick={() => handleSort('created_at')}
                            >
                              <div className="flex items-center gap-1">
                                Created
                                {renderSortIcon('created_at')}
                              </div>
                            </TableHead>
                            <TableHead>Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {students.map((student) => (
                            <TableRow key={student.id}>
                              <TableCell className="font-medium">{student.name}</TableCell>
                              <TableCell>
                                <Badge variant="outline" className="font-mono">
                                  {student.admission_number}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-sm text-gray-600 hidden md:table-cell">
                                {student.email || '-'}
                              </TableCell>
                              <TableCell className="hidden md:table-cell">{student.hostel || '-'}</TableCell>
                              <TableCell className="hidden md:table-cell">
                                {student.route ? (
                                  <Badge variant="secondary">{student.route}</Badge>
                                ) : (
                                  '-'
                                )}
                              </TableCell>
                              <TableCell className="hidden lg:table-cell">
                                {student.created_at ? new Date(student.created_at).toLocaleDateString() : '-'}
                              </TableCell>
                              <TableCell>
                                <div className="flex gap-2">
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => {
                                      setEditingStudent(student);
                                      setIsModalOpen(true);
                                    }}
                                    className="min-h-[36px]"
                                  >
                                    <Edit className="w-4 h-4" />
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    className="text-red-600 border-red-600 hover:bg-red-50 min-h-[36px]"
                                    onClick={() => handleDeleteStudent(student)}
                                  >
                                    <Trash2 className="w-4 h-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>

                    {/* Pagination - Mobile Optimized */}
                    {pagination.totalPages > 1 && (
                      <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 px-4 md:px-6">
                        <div className="text-sm text-gray-600 text-center sm:text-left w-full sm:w-auto order-2 sm:order-1">
                          Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                          {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                          {pagination.total} students
                        </div>
                        <div className="flex items-center gap-2 w-full sm:w-auto justify-center sm:justify-end order-1 sm:order-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(pagination.page - 1)}
                            disabled={pagination.page === 1}
                            className="min-h-[40px] px-3"
                          >
                            <ChevronLeft className="w-4 h-4 sm:mr-2" />
                            <span className="hidden sm:inline">Previous</span>
                          </Button>
                          
                          {/* Hide page numbers on very small screens */}
                          <div className="hidden xs:flex items-center gap-1">
                            {Array.from({ length: Math.min(3, pagination.totalPages) }, (_, i) => {
                              // Show pages around current page
                              let pageNum;
                              if (pagination.totalPages <= 3) {
                                pageNum = i + 1;
                              } else if (pagination.page === 1) {
                                pageNum = i + 1;
                              } else if (pagination.page === pagination.totalPages) {
                                pageNum = pagination.totalPages - 2 + i;
                              } else {
                                pageNum = pagination.page - 1 + i;
                              }
                              
                              return (
                                <Button
                                  key={pageNum}
                                  variant={pagination.page === pageNum ? "default" : "outline"}
                                  size="sm"
                                  onClick={() => handlePageChange(pageNum)}
                                  className="w-9 h-9 p-0 min-h-[36px]"
                                >
                                  {pageNum}
                                </Button>
                              );
                            })}
                          </div>
                          
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(pagination.page + 1)}
                            disabled={pagination.page === pagination.totalPages}
                            className="min-h-[40px] px-3"
                          >
                            <span className="hidden sm:inline">Next</span>
                            <ChevronRight className="w-4 h-4 sm:ml-2" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </CardContent>
            </Card>
          </motion.div>

          {/* Student Modal */}
          <StudentModal
            key={editingStudent ? editingStudent.id : 'add'}
            open={isModalOpen}
            setOpen={setIsModalOpen}
            onSubmit={handleStudentSubmit}
            editingStudent={editingStudent}
            formLoading={formLoading}
            routeOptions={routeOptions}
            hostelOptions={hostelOptions}
          />
        </div>
      </div>
    </PageTransition>
  );
}

export default withAdminAuth(StudentManagement);

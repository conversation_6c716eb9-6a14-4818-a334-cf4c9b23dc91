'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
// import { AnimatedBus } from '@/components/ui/animated-bus'; // Animated bus commented out
import { PageTransition } from '@/components/ui/page-transition';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { AlertCircle, Shield, Users } from 'lucide-react';
import { motion } from 'framer-motion';
import { toast } from 'sonner';

export default function HomePage() {
  const [isLoading, setIsLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [loginType, setLoginType] = useState<'admin' | 'staff' | null>(null);
  const router = useRouter();

  const handleNext = async () => {
    setIsLoading(true);
    try {
      // Add cache-busting by using timestamp and no-cache options
      const timestamp = Date.now();
      const response = await fetch(`/api/booking-status?t=${timestamp}`, {
        cache: 'no-store',
        headers: {
          'Pragma': 'no-cache'
        }
      });
      const data = await response.json();
      
      if (data.enabled) {
        router.push('/details');
      } else {
        setShowModal(true);
      }
    } catch (error) {
      toast.error('Failed to check booking status. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLoginClick = (type: 'admin' | 'staff') => {
    setLoginType(type);
    setShowLoginModal(true);
  };

  return (
    <PageTransition>
      <div className="min-h-screen flex flex-col">
        {/* Navigation Bar */}
        <nav className="bg-white/80 backdrop-blur-sm border-b border-gray-200 shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center">
                {/* Removed "Bus Pass System" text */}
              </div>
              <div className="flex items-center gap-3">
                <Button
                  onClick={() => handleLoginClick('admin')}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2 text-red-600 hover:text-red-800 border-red-200 hover:border-red-300"
                >
                  <Shield className="w-4 h-4" />
                  <span className="hidden sm:inline">Admin</span>
                </Button>
                <Button
                  onClick={() => handleLoginClick('staff')}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2 text-blue-600 hover:text-blue-800 border-blue-200 hover:border-blue-300"
                >
                  <Users className="w-4 h-4" />
                  <span className="hidden sm:inline">Staff</span>
                </Button>
              </div>
            </div>
          </div>
        </nav>

        {/* Header Section */}
        <div className="bg-gradient-to-br from-blue-50 via-white to-green-50 py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: -50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-4xl md:text-6xl font-bold text-blue-900 mb-4 leading-tight">
                St. Joseph's College of<br />
                Engineering and Technology
              </h1>
              <p className="text-xl md:text-2xl text-green-700 font-semibold">Palai</p>
            </motion.div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col items-center justify-center p-4 bg-gradient-to-br from-blue-50 via-white to-green-50">
          {/* <AnimatedBus /> */}

          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="space-y-6"
          >
            <Card className="w-full max-w-md shadow-2xl border-0 bg-white/80 backdrop-blur-sm">
              <CardContent className="p-8 text-center">
                <h2 className="text-2xl font-bold text-gray-800 mb-6">
                  Bus Pass Booking System
                </h2>
                <p className="text-gray-600 mb-8">
                  Book your bus pass quickly and easily. Click next to get started.
                </p>
                <Button
                  onClick={handleNext}
                  disabled={isLoading}
                  size="lg"
                  className="w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white font-semibold py-3 rounded-lg transition-all duration-300 transform hover:scale-105"
                >
                  {isLoading ? 'Checking...' : 'Next'}
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Booking Not Available Modal */}
        <Dialog open={showModal} onOpenChange={setShowModal}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2 text-orange-600">
                <AlertCircle className="w-5 h-5" />
                Booking Not Available
              </DialogTitle>
            </DialogHeader>
            <div className="text-center py-4">
              <p className="text-gray-600">
                Sorry! Booking has not started yet!
              </p>
              <Button
                onClick={() => setShowModal(false)}
                className="mt-4"
                variant="outline"
              >
                OK
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* Login Selection Modal */}
        <Dialog open={showLoginModal} onOpenChange={setShowLoginModal}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2 text-blue-600">
                {loginType === 'admin' ? (
                  <>
                    <Shield className="w-5 h-5" />
                    Admin Login
                  </>
                ) : (
                  <>
                    <Users className="w-5 h-5" />
                    Staff Login
                  </>
                )}
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <p className="text-gray-600 text-center mb-6">
                {loginType === 'admin' 
                  ? 'Enter your admin credentials to access the system'
                  : 'Enter your staff credentials to access the system'
                }
              </p>

              <div className="space-y-3">
                {loginType === 'admin' ? (
                  <Button
                    onClick={() => {
                      setShowLoginModal(false);
                      router.push('/admin');
                    }}
                    className="w-full bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 text-white font-semibold py-3 rounded-lg transition-all duration-300 transform hover:scale-105"
                    size="lg"
                  >
                    <Shield className="w-4 h-4 mr-2" />
                    Admin Login
                  </Button>
                ) : (
                  <Button
                    onClick={() => {
                      setShowLoginModal(false);
                      router.push('/staff');
                    }}
                    className="w-full bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 rounded-lg transition-all duration-300 transform hover:scale-105"
                    size="lg"
                  >
                    <Users className="w-4 h-4 mr-2" />
                    Staff Login
                  </Button>
                )}
              </div>

              <Button
                onClick={() => setShowLoginModal(false)}
                variant="outline"
                className="w-full mt-4"
              >
                Cancel
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </PageTransition>
  );
}
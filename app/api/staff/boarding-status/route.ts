export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { withStaffAuth, getStaff, createApiResponse, handleApiError } from '@/lib/middleware';
import { supabaseAdmin } from '@/lib/supabase';

/**
 * PATCH /api/staff/boarding-status - Update boarding status for a booking
 * 
 * This endpoint allows staff members to mark bookings as boarded when students
 * physically board the bus. It implements different update behaviors based on
 * the source interface to maintain data consistency and proper access control.
 * 
 * Authorization: Staff role required
 * 
 * Request Body:
 * {
 *   "booking_id": string,
 *   "boarded": boolean,
 *   "source": "dashboard" | "historical" (required)
 * }
 * 
 * Source Behavior:
 * - "dashboard": Updates both bookings and current_bookings tables atomically for all bookings
 * - "historical": Only allows updates for special bookings (is_special = true) in bookings table only
 * 
 * Response:
 * {
 *   "success": boolean,
 *   "data": {
 *     "booking": BookingObject,
 *     "message": string
 *   }
 * }
 */
export async function PATCH(request: NextRequest) {
  return withStaffAuth(request, async (req) => {
    try {
      const staff = getStaff(req);
      if (!staff) {
        return NextResponse.json(
          { 
            success: false,
            error: 'Staff authentication required' 
          },
          { status: 401 }
        );
      }

      const body = await req.json();
      const { booking_id, boarded, source } = body;

      // Validate request body - handle both string and number types for booking_id
      if ((typeof booking_id !== 'string' && typeof booking_id !== 'number') || 
          (typeof booking_id === 'string' && !booking_id.trim()) ||
          typeof boarded !== 'boolean') {
        return NextResponse.json(
          { 
            success: false,
            error: 'Invalid request body. booking_id (string or number) and boarded (boolean) are required.' 
          },
          { status: 400 }
        );
      }

      // Convert booking_id to string for consistency
      const bookingIdString = String(booking_id);

      // Verify the booking exists in the main bookings table (always required)
      const { data: mainBooking, error: mainFetchError } = await supabaseAdmin
        .from('bookings')
        .select('booking_id, admission_number, student_name, bus_route, destination, go_date, return_date, boarded, is_special')
        .eq('booking_id', bookingIdString)
        .single();

      if (mainFetchError || !mainBooking) {
        console.error('Main booking lookup error:', mainFetchError);
        return NextResponse.json(
          { 
            success: false,
            error: 'Booking not found in main bookings table' 
          },
          { status: 404 }
        );
      }

      // For historical source, validate that only special bookings can be updated
      if (source === 'historical') {
        if (!mainBooking.is_special) {
          return NextResponse.json(
            { 
              success: false,
              error: 'Update boarding in staff dashboard',
              details: 'Regular student bookings cannot be updated from historical bookings interface. Please use the staff dashboard for boarding status updates.'
            },
            { status: 403 }
          );
        }
      }

      // For dashboard source, also verify the booking exists in current_bookings table
      let currentBooking = null;
      if (source === 'dashboard') {
        const { data: currentBookingData, error: currentFetchError } = await supabaseAdmin
          .from('current_bookings')
          .select('booking_id, admission_number, student_name, bus_route, destination, go_date, return_date, boarded')
          .eq('booking_id', bookingIdString)
          .single();

        if (currentFetchError || !currentBookingData) {
          console.error('Current booking lookup error:', currentFetchError);
          return NextResponse.json(
            { 
              success: false,
              error: 'Booking not found in current bookings table. Dashboard updates require the booking to exist in both tables.' 
            },
            { status: 404 }
          );
        }
        currentBooking = currentBookingData;
      }

      // Use the appropriate booking data for consistency checks
      const booking = source === 'dashboard' ? currentBooking : mainBooking;

      // Verify staff can only update bookings for their assigned route
      if (booking.bus_route !== staff.route_code) {
        return NextResponse.json(
          { 
            success: false,
            error: 'Access denied: You can only update boarding status for your assigned route' 
          },
          { status: 403 }
        );
      }

      // Check if the booking is already in the requested state
      if (booking.boarded === boarded) {
        return NextResponse.json(
          {
            success: true,
            data: {
              booking,
              message: `Booking is already ${boarded ? 'boarded' : 'not boarded'}`
            }
          }
        );
      }

      // Update tables based on source
      if (source === 'dashboard') {
        // Dashboard source: Update both tables atomically for all bookings
        // First, update the bookings table
        const { error: bookingsUpdateError } = await supabaseAdmin
          .from('bookings',)
          .update({ boarded: boarded })
          .eq('booking_id', bookingIdString);

        if (bookingsUpdateError) {
          console.error('Bookings table update error:', bookingsUpdateError);
          return NextResponse.json(
            { 
              success: false,
              error: 'Failed to update booking status',
              details: bookingsUpdateError.message
            },
            { status: 500 }
          );
        }

        // Then, update the current_bookings table
        const { error: currentBookingsUpdateError } = await supabaseAdmin
          .from('current_bookings')
          .update({ boarded: boarded })
          .eq('booking_id', bookingIdString);

        if (currentBookingsUpdateError) {
          console.error('Current bookings table update error:', currentBookingsUpdateError);
          
          // If current_bookings update fails, we should rollback the bookings table update
          // This maintains consistency between the two tables
          const { error: rollbackError } = await supabaseAdmin
            .from('bookings')
            .update({ boarded: !boarded })
            .eq('booking_id', bookingIdString);
          
          if (rollbackError) {
            console.error('Rollback error:', rollbackError);
          }
          
          return NextResponse.json(
            { 
              success: false,
              error: 'Failed to update current booking status',
              details: currentBookingsUpdateError.message
            },
            { status: 500 }
          );
        }

        // Fetch the updated booking data from current_bookings for response
        const { data: updatedBooking, error: fetchUpdatedError } = await supabaseAdmin
          .from('current_bookings')
          .select('booking_id, admission_number, student_name, bus_route, destination, go_date, return_date, boarded')
          .eq('booking_id', bookingIdString)
          .single();

        if (fetchUpdatedError) {
          console.error('Error fetching updated booking:', fetchUpdatedError);
          // Even if we can't fetch the updated data, the update was successful
          return createApiResponse({
            booking: { ...booking, boarded: boarded },
            message: `Booking successfully marked as ${boarded ? 'boarded' : 'not boarded'} (both tables updated)`,
            updated_by: staff.username,
            updated_at: new Date().toISOString(),
            source: source
          });
        }

        // Log the boarding status change for audit purposes
        console.log(`Staff ${staff.username} (${staff.route_code}) updated boarding status from dashboard:`, {
          booking_id: bookingIdString,
          student_name: booking.student_name,
          admission_number: booking.admission_number,
          route: booking.bus_route,
          destination: booking.destination,
          previous_status: booking.boarded,
          new_status: boarded,
          source: source,
          is_special: mainBooking.is_special,
          timestamp: new Date().toISOString()
        });

        return createApiResponse({
          booking: updatedBooking,
          message: `Booking successfully marked as ${boarded ? 'boarded' : 'not boarded'} (both tables updated)`,
          updated_by: staff.username,
          updated_at: new Date().toISOString(),
          source: source
        });

      } else {
        // Historical source: Update only the bookings table for special bookings only
        const { error: bookingsUpdateError } = await supabaseAdmin
          .from('bookings')
          .update({ boarded: boarded })
          .eq('booking_id', bookingIdString);

        if (bookingsUpdateError) {
          console.error('Bookings table update error:', bookingsUpdateError);
          return NextResponse.json(
            { 
              success: false,
              error: 'Failed to update booking status',
              details: bookingsUpdateError.message
            },
            { status: 500 }
          );
        }

        // Fetch the updated booking data from bookings table for response
        const { data: updatedBooking, error: fetchUpdatedError } = await supabaseAdmin
          .from('bookings')
          .select('booking_id, admission_number, student_name, bus_route, destination, go_date, return_date, boarded, is_special')
          .eq('booking_id', bookingIdString)
          .single();

        if (fetchUpdatedError) {
          console.error('Error fetching updated booking:', fetchUpdatedError);
          // Even if we can't fetch the updated data, the update was successful
          return createApiResponse({
            booking: { ...booking, boarded: boarded },
            message: `Special booking successfully marked as ${boarded ? 'boarded' : 'not boarded'} (bookings table only)`,
            updated_by: staff.username,
            updated_at: new Date().toISOString(),
            source: source
          });
        }

        // Log the boarding status change for audit purposes
        console.log(`Staff ${staff.username} (${staff.route_code}) updated boarding status from historical (special booking):`, {
          booking_id: bookingIdString,
          student_name: booking.student_name,
          admission_number: booking.admission_number,
          route: booking.bus_route,
          destination: booking.destination,
          previous_status: booking.boarded,
          new_status: boarded,
          source: source,
          is_special: true,
          timestamp: new Date().toISOString()
        });

        return createApiResponse({
          booking: updatedBooking,
          message: `Special booking successfully marked as ${boarded ? 'boarded' : 'not boarded'} (bookings table only)`,
          updated_by: staff.username,
          updated_at: new Date().toISOString(),
          source: source
        });
      }

    } catch (error) {
      console.error('Boarding status update error:', error);
      return handleApiError(error, 'Failed to update boarding status');
    }
  });
}

/**
 * GET /api/staff/boarding-status - Get boarding statistics for staff route
 * 
 * This endpoint provides boarding statistics for the staff member's assigned route,
 * including total bookings, boarded count, and boarding rate.
 * 
 * Authorization: Staff role required
 * 
 * Query Parameters:
 * - date: Optional date filter (YYYY-MM-DD format)
 * 
 * Response:
 * {
 *   "success": boolean,
 *   "data": {
 *     "route_code": string,
 *     "total_bookings": number,
 *     "boarded_count": number,
 *     "not_boarded_count": number,
 *     "boarding_rate": string
 *   }
 * }
 */
export async function GET(request: NextRequest) {
  return withStaffAuth(request, async (req) => {
    try {
      const staff = getStaff(req);
      if (!staff) {
        return NextResponse.json(
          { 
            success: false,
            error: 'Staff authentication required' 
          },
          { status: 401 }
        );
      }

      const { searchParams } = new URL(request.url);
      const dateFilter = searchParams.get('date');

      // Build query for current bookings in staff's route
      let query = supabaseAdmin
        .from('current_bookings')
        .select('booking_id, boarded')
        .eq('bus_route', staff.route_code);

      // Apply date filter if provided
      if (dateFilter) {
        query = query.eq('go_date', dateFilter);
      }

      const { data: bookings, error } = await query;

      if (error) {
        throw error;
      }

      // Calculate statistics
      const totalBookings = bookings?.length || 0;
      const boardedCount = bookings?.filter((b: { boarded: boolean }) => b.boarded).length || 0;
      const notBoardedCount = totalBookings - boardedCount;
      const boardingRate = totalBookings > 0 
        ? ((boardedCount / totalBookings) * 100).toFixed(1)
        : '0.0';

      return createApiResponse({
        route_code: staff.route_code,
        total_bookings: totalBookings,
        boarded_count: boardedCount,
        not_boarded_count: notBoardedCount,
        boarding_rate: `${boardingRate}%`,
        date_filter: dateFilter || 'all',
        generated_at: new Date().toISOString()
      });

    } catch (error) {
      console.error('Boarding statistics error:', error);
      return handleApiError(error, 'Failed to fetch boarding statistics');
    }
  });
}

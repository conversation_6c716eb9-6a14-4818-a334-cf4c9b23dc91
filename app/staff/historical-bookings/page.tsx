'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { PageTransition } from '@/components/ui/page-transition';
import { BoardingConfirmationModal } from '@/components/ui/boarding-confirmation-modal';
import { useStaff, withStaffAuth } from '@/contexts/StaffContext';
import { ArrowLeft, Crown, Users, Bus, Calendar, RefreshCw, Clock, TrendingUp, FileText, CheckCircle, XCircle } from 'lucide-react';
import { motion } from 'framer-motion';
import { toast } from 'sonner';

interface HistoricalBooking {
  id: number;
  admission_number: string;
  student_name: string;
  bus_route: string;
  destination: string;
  created_at: string;
  go_date: string | null;
  return_date: string | null;
  is_special: boolean | null;
  boarded: boolean;
  payment_status: boolean;
  fare: number | null;
}

function HistoricalBookingsPage() {
  const { staff } = useStaff();
  const [bookings, setBookings] = useState<HistoricalBooking[]>([]);
  const [busName, setBusName] = useState<string>('Unknown Bus');
  const [isLoading, setIsLoading] = useState(true);
  const [selectedBooking, setSelectedBooking] = useState<HistoricalBooking | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isUpdatingBoarding, setIsUpdatingBoarding] = useState(false);
  const router = useRouter();

  const fetchHistoricalBookings = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/staff/historical-bookings?route=${staff?.route_code}`, {
        credentials: 'include'
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setBookings(result.data.bookings || []);
          setBusName(result.data.bus_name || 'Unknown Bus');
        } else {
          toast.error('Failed to fetch historical bookings');
        }
      } else {
        toast.error('Failed to fetch historical bookings');
      }
    } catch (error) {
      console.error('Error fetching historical bookings:', error);
      toast.error('Failed to fetch historical bookings');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (staff?.route_code) {
      fetchHistoricalBookings();
    }
  }, [staff?.route_code]);

  const handleBackToDashboard = () => {
    router.push('/staff/dashboard');
  };

  const handleBoardingClick = (booking: HistoricalBooking) => {
    if (booking.boarded) {
      // Already boarded, no action needed
      return;
    }
    setSelectedBooking(booking);
    setIsModalOpen(true);
  };

  const handleBoardingConfirm = async (bookingId: number) => {
    try {
      setIsUpdatingBoarding(true);

      const response = await fetch('/api/staff/boarding-status', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          booking_id: bookingId,
          boarded: true
        })
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to update boarding status');
      }

      // Update the booking in the local state
      setBookings(prevBookings =>
        prevBookings.map(booking =>
          booking.id === bookingId
            ? { ...booking, boarded: true }
            : booking
        )
      );

      toast.success('Boarding status updated successfully');
    } catch (error) {
      console.error('Error updating boarding status:', error);
      throw error; // Re-throw to be handled by the modal
    } finally {
      setIsUpdatingBoarding(false);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const specialBookingsCount = bookings.filter(booking => booking.is_special).length;
  const regularBookingsCount = bookings.length - specialBookingsCount;

  return (
    <PageTransition>
      <div className="min-h-screen bg-slate-50">
        {/* Header */}
        <div className="bg-white border-b border-slate-200/60 backdrop-blur-xl">
          <div className="max-w-7xl mx-auto px-6 py-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
              {/* Left side - Navigation and Title */}
              <div className="flex items-center gap-4">
                <Button
                  onClick={handleBackToDashboard}
                  variant="outline"
                  size="sm"
                  className="bg-white hover:bg-slate-50 border-slate-200 text-slate-700 hover:text-slate-900"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Dashboard
                </Button>
                
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 via-purple-600 to-violet-600 rounded-xl flex items-center justify-center shadow-lg">
                  <FileText className="w-6 h-6 text-white" />
                </div>
                
                <div>
                  <h1 className="text-2xl font-bold text-slate-900">Historical Bookings</h1>
                  <p className="text-slate-600">Complete booking history for {staff?.route_code}</p>
                </div>
              </div>

              {/* Right side - Route Info and Actions */}
              <div className="flex flex-wrap items-center gap-3">
                <Badge variant="secondary" className="px-4 py-2 text-sm font-medium bg-purple-50 text-purple-700 border-purple-200">
                  <Bus className="w-4 h-4 mr-2" />
                  Route {staff?.route_code}
                </Badge>
                
                <Button
                  onClick={fetchHistoricalBookings}
                  variant="outline"
                  size="sm"
                  disabled={isLoading}
                  className="bg-white hover:bg-slate-50 border-slate-200"
                >
                  <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-6 py-8">
          {/* Stats Section */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {/* Total Historical Bookings */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <Card className="border-0 shadow-sm bg-gradient-to-br from-blue-50 to-indigo-50 hover:shadow-md transition-all duration-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-slate-600 mb-1">Total Bookings</p>
                      <p className="text-3xl font-bold text-slate-900">{bookings.length}</p>
                      <p className="text-xs text-slate-500 mt-1">All time data</p>
                    </div>
                    <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                      <Users className="w-6 h-6 text-blue-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Regular Bookings */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Card className="border-0 shadow-sm bg-gradient-to-br from-green-50 to-emerald-50 hover:shadow-md transition-all duration-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-slate-600 mb-1">Regular Bookings</p>
                      <p className="text-3xl font-bold text-slate-900">{regularBookingsCount}</p>
                      <p className="text-xs text-slate-500 mt-1">Standard passes</p>
                    </div>
                    <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                      <Calendar className="w-6 h-6 text-green-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Special Bookings */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <Card className="border-0 shadow-sm bg-gradient-to-br from-amber-50 to-orange-50 hover:shadow-md transition-all duration-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-slate-600 mb-1">Special Passes</p>
                      <p className="text-3xl font-bold text-slate-900">{specialBookingsCount}</p>
                      <p className="text-xs text-slate-500 mt-1">Admin bookings</p>
                    </div>
                    <div className="w-12 h-12 bg-amber-100 rounded-xl flex items-center justify-center">
                      <Crown className="w-6 h-6 text-amber-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Historical Bookings Table */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <Card className="border-0 shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-xl">
                  <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                    <TrendingUp className="w-4 h-4 text-purple-600" />
                  </div>
                  Complete Booking History
                  <Badge variant="secondary" className="ml-2 text-xs">
                    Route {staff?.route_code}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex items-center justify-center py-12">
                    <div className="flex items-center gap-3">
                      <div className="animate-spin rounded-full h-6 w-6 border-2 border-purple-600 border-t-transparent"></div>
                      <span className="text-slate-600">Loading historical data...</span>
                    </div>
                  </div>
                ) : bookings.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <FileText className="w-8 h-8 text-slate-400" />
                    </div>
                    <h3 className="text-lg font-medium text-slate-900 mb-2">No historical bookings</h3>
                    <p className="text-slate-500">No booking history found for route {staff?.route_code}</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow className="border-slate-200 hover:bg-transparent">
                          <TableHead className="font-semibold text-slate-700">Student Details</TableHead>
                          <TableHead className="font-semibold text-slate-700">Admission No.</TableHead>
                          <TableHead className="font-semibold text-slate-700">Destination</TableHead>
                          <TableHead className="font-semibold text-slate-700">Travel Dates</TableHead>
                          <TableHead className="font-semibold text-slate-700">Booked On</TableHead>
                          <TableHead className="font-semibold text-slate-700">Boarding Status</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {bookings.map((booking) => (
                          <TableRow
                            key={booking.id}
                            className={`border-slate-100 hover:bg-slate-50/50 transition-colors ${
                              booking.boarded ? 'bg-green-50 border-green-200' : ''
                            }`}
                          >
                            <TableCell className="font-medium text-slate-900">
                              <div className="flex items-center gap-2">
                                {booking.is_special && (
                                  <div className="w-6 h-6 bg-amber-100 rounded-full flex items-center justify-center">
                                    <Crown className="w-3 h-3 text-amber-600" />
                                  </div>
                                )}
                                <span>{booking.student_name}</span>
                                {booking.is_special && (
                                  <Badge variant="secondary" className="ml-2 text-xs bg-amber-50 text-amber-700 border-amber-200">
                                    Special
                                  </Badge>
                                )}
                              </div>
                            </TableCell>
                            <TableCell className="text-slate-600 font-mono text-sm">
                              {booking.admission_number}
                            </TableCell>
                            <TableCell className="text-slate-700">
                              {booking.destination}
                            </TableCell>
                            <TableCell className="text-slate-600">
                              <div className="space-y-1">
                                <div className="flex items-center gap-2 text-sm">
                                  <Clock className="w-3 h-3 text-slate-400" />
                                  <span>Go: {formatDate(booking.go_date)}</span>
                                </div>
                                <div className="flex items-center gap-2 text-sm">
                                  <Clock className="w-3 h-3 text-slate-400" />
                                  <span>Return: {formatDate(booking.return_date)}</span>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell className="text-slate-600 text-sm">
                              {formatDateTime(booking.created_at)}
                            </TableCell>
                            <TableCell>
                              {booking.boarded ? (
                                <Badge variant="default" className="bg-green-600 hover:bg-green-700 text-white">
                                  <CheckCircle className="w-3 h-3 mr-1" />
                                  Boarded
                                </Badge>
                              ) : (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleBoardingClick(booking)}
                                  className="border-red-300 text-red-700 hover:bg-red-50 hover:border-red-400"
                                  disabled={isUpdatingBoarding}
                                >
                                  <XCircle className="w-3 h-3 mr-1" />
                                  Not Boarded
                                </Button>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>

      {/* Boarding Confirmation Modal */}
      <BoardingConfirmationModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        booking={selectedBooking}
        onConfirm={handleBoardingConfirm}
        isLoading={isUpdatingBoarding}
      />
    </PageTransition>
  );
}

export default withStaffAuth(HistoricalBookingsPage);
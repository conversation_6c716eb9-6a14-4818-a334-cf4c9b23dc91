'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { PageTransition } from '@/components/ui/page-transition';
import { BoardingConfirmationModal } from '@/components/ui/boarding-confirmation-modal';
import { useStaff, withStaffAuth } from '@/contexts/StaffContext';
import { LogOut, Users, Bus, Calendar, RefreshCw, History, TrendingUp, Clock, CheckCircle, XCircle } from 'lucide-react';
import { motion } from 'framer-motion';
import { toast } from 'sonner';

interface Booking {
  booking_id: string;
  admission_number: string;
  student_name: string;
  bus_route: string;
  destination: string;
  payment_status: boolean;
  created_at: string;
  go_date: string | null;
  return_date: string | null;
  fare: number | null;
  bus_name: string;
  boarded: boolean;
}

interface BookingStats {
  total: number;
}

function StaffDashboard() {
  const { staff, logout } = useStaff();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [stats, setStats] = useState<BookingStats>({ total: 0 });
  const [busName, setBusName] = useState<string>('Unknown Bus');
  const [isLoading, setIsLoading] = useState(true);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isUpdatingBoarding, setIsUpdatingBoarding] = useState(false);
  const router = useRouter();

  const fetchBookings = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/staff/bookings?route=${staff?.route_code}`, {
        credentials: 'include'
      });

      if (response.ok) {
        const result = await response.json();
        console.log('API Response:', result);
        
        if (result.success) {
          setBookings(result.data.bookings || []);
          setStats(result.data.stats || { total: 0 });
          setBusName(result.data.bus_name || 'Unknown Bus');
        } else {
          console.error('API returned success: false:', result);
          toast.error(result.error || 'Failed to fetch bookings');
        }
      } else {
        const errorData = await response.json().catch(() => ({}));
        console.error('API error response:', errorData);
        toast.error(errorData.error || 'Failed to fetch bookings');
      }
    } catch (error) {
      console.error('Error fetching bookings:', error);
      toast.error('Failed to fetch bookings');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (staff?.route_code) {
      fetchBookings();
    }
  }, [staff?.route_code]);

  const handleLogout = async () => {
    await logout();
  };

  const handleViewHistoricalBookings = () => {
    router.push('/staff/historical-bookings');
  };

  const handleBoardingClick = (booking: Booking) => {
    if (booking.boarded) {
      // Already boarded, no action needed
      return;
    }
    setSelectedBooking(booking);
    setIsModalOpen(true);
  };

  const handleBoardingConfirm = async (bookingId: string) => {
    try {
      setIsUpdatingBoarding(true);

      const response = await fetch('/api/staff/boarding-status', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          booking_id: bookingId,
          boarded: true
        })
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to update boarding status');
      }

      // Update the booking in the local state
      setBookings(prevBookings =>
        prevBookings.map(booking =>
          booking.booking_id === bookingId
            ? { ...booking, boarded: true }
            : booking
        )
      );

      toast.success('Boarding status updated successfully');
    } catch (error) {
      console.error('Error updating boarding status:', error);
      throw error; // Re-throw to be handled by the modal
    } finally {
      setIsUpdatingBoarding(false);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatDateTime = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number | null) => {
    if (amount === null) return 'N/A';
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount);
  };

  return (
    <PageTransition>
      <div className="min-h-screen bg-slate-50">
        {/* Header */}
        <div className="bg-white border-b border-slate-200/60 backdrop-blur-xl">
          <div className="max-w-7xl mx-auto px-6 py-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
              {/* Left side - Welcome and Route Info */}
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Users className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-slate-900">Staff Dashboard</h1>
                  <p className="text-slate-600">Welcome back, {staff?.username}</p>
                </div>
              </div>

              {/* Right side - Actions */}
              <div className="flex flex-wrap items-center gap-3">
                <Badge variant="secondary" className="px-4 py-2 text-sm font-medium bg-blue-50 text-blue-700 border-blue-200">
                  <Bus className="w-4 h-4 mr-2" />
                  Route {staff?.route_code} • {busName}
                </Badge>
                
                <Button
                  onClick={handleViewHistoricalBookings}
                  variant="outline"
                  size="sm"
                  className="bg-white hover:bg-slate-50 border-slate-200 text-slate-700 hover:text-slate-900"
                >
                  <History className="w-4 h-4 mr-2" />
                  All Time Bookings
                </Button>
                
                <Button
                  onClick={fetchBookings}
                  variant="outline"
                  size="sm"
                  disabled={isLoading}
                  className="bg-white hover:bg-slate-50 border-slate-200"
                >
                  <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
                
                <Button
                  onClick={handleLogout}
                  variant="outline"
                  size="sm"
                  className="text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200"
                >
                  <LogOut className="w-4 h-4 mr-2" />
                  Logout
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-6 py-8">
          {/* Stats Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {/* Total Bookings Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <Card className="border-0 shadow-sm bg-gradient-to-br from-blue-50 to-indigo-50 hover:shadow-md transition-all duration-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-slate-600 mb-1">Total Bookings</p>
                      <p className="text-3xl font-bold text-slate-900">{stats.total}</p>
                      <p className="text-xs text-slate-500 mt-1">Current route bookings</p>
                    </div>
                    <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                      <Users className="w-6 h-6 text-blue-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Route Info Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Card className="border-0 shadow-sm bg-gradient-to-br from-green-50 to-emerald-50 hover:shadow-md transition-all duration-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-slate-600 mb-1">Route Code</p>
                      <p className="text-3xl font-bold text-slate-900">{staff?.route_code}</p>
                      <p className="text-xs text-slate-500 mt-1">{busName}</p>
                    </div>
                    <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                      <Bus className="w-6 h-6 text-green-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Quick Actions Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <Card 
                className="border-0 shadow-sm bg-gradient-to-br from-purple-50 to-violet-50 hover:shadow-md transition-all duration-200 cursor-pointer hover:scale-105"
                onClick={handleViewHistoricalBookings}
              >
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-slate-600 mb-1">Quick Actions</p>
                      <p className="text-lg font-semibold text-slate-900">View History</p>
                      <p className="text-xs text-slate-500 mt-1">Access all time data</p>
                    </div>
                    <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                      <TrendingUp className="w-6 h-6 text-purple-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Current Bookings Table */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <Card className="border-0 shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-3 text-xl">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Bus className="w-4 h-4 text-blue-600" />
                  </div>
                  Current Route Bookings
                  <Badge variant="secondary" className="ml-2 text-xs">
                    Route {staff?.route_code}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex items-center justify-center py-12">
                    <div className="flex items-center gap-3">
                      <div className="animate-spin rounded-full h-6 w-6 border-2 border-blue-600 border-t-transparent"></div>
                      <span className="text-slate-600">Loading bookings...</span>
                    </div>
                  </div>
                ) : bookings.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Calendar className="w-8 h-8 text-slate-400" />
                    </div>
                    <h3 className="text-lg font-medium text-slate-900 mb-2">No current bookings</h3>
                    <p className="text-slate-500">No bookings found for route {staff?.route_code}</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow className="border-slate-200 hover:bg-transparent">
                          <TableHead className="font-semibold text-slate-700">Booking ID</TableHead>
                          <TableHead className="font-semibold text-slate-700">Student</TableHead>
                          <TableHead className="font-semibold text-slate-700">Admission No.</TableHead>
                          <TableHead className="font-semibold text-slate-700">Destination</TableHead>
                          <TableHead className="font-semibold text-slate-700">Travel Dates</TableHead>
                          <TableHead className="font-semibold text-slate-700">Booked On</TableHead>
                          <TableHead className="font-semibold text-slate-700">Boarding Status</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {bookings.map((booking) => (
                          <TableRow
                            key={booking.booking_id}
                            className={`border-slate-100 hover:bg-slate-50/50 transition-colors ${
                              booking.boarded ? 'bg-green-50 border-green-200' : ''
                            }`}
                          >
                            <TableCell className="font-mono text-sm font-medium text-slate-900">
                              {booking.booking_id}
                            </TableCell>
                            <TableCell className="font-medium text-slate-900">
                              {booking.student_name}
                            </TableCell>
                            <TableCell className="text-slate-600 font-mono text-sm">
                              {booking.admission_number}
                            </TableCell>
                            <TableCell className="text-slate-700">
                              {booking.destination}
                            </TableCell>
                            <TableCell className="text-slate-600">
                              <div className="space-y-1">
                                <div className="flex items-center gap-2 text-sm">
                                  <Clock className="w-3 h-3 text-slate-400" />
                                  <span>Go: {formatDate(booking.go_date)}</span>
                                </div>
                                <div className="flex items-center gap-2 text-sm">
                                  <Clock className="w-3 h-3 text-slate-400" />
                                  <span>Return: {formatDate(booking.return_date)}</span>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell className="text-slate-600 text-sm">
                              {formatDateTime(booking.created_at)}
                            </TableCell>
                            <TableCell>
                              {booking.boarded ? (
                                <Badge variant="default" className="bg-green-600 hover:bg-green-700 text-white">
                                  <CheckCircle className="w-3 h-3 mr-1" />
                                  Boarded
                                </Badge>
                              ) : (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleBoardingClick(booking)}
                                  className="border-red-300 text-red-700 hover:bg-red-50 hover:border-red-400"
                                  disabled={isUpdatingBoarding}
                                >
                                  <XCircle className="w-3 h-3 mr-1" />
                                  Not Boarded
                                </Button>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>

      {/* Boarding Confirmation Modal */}
      <BoardingConfirmationModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        booking={selectedBooking}
        onConfirm={handleBoardingConfirm}
        isLoading={isUpdatingBoarding}
      />
    </PageTransition>
  );
}

export default withStaffAuth(StaffDashboard);

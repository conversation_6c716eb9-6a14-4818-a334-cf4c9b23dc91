'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { CheckCircle, AlertCircle, User, MapPin, Calendar, Route, CreditCard } from 'lucide-react';
import { toast } from 'sonner';

interface BookingDetails {
  id: number;
  student_name: string;
  admission_number: string;
  bus_route: string;
  destination: string;
  go_date: string | null;
  return_date: string | null;
  payment_status: boolean;
  fare: number | null;
  boarded: boolean;
}

interface BoardingConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  booking: BookingDetails | null;
  onConfirm: (bookingId: number) => Promise<void>;
  isLoading?: boolean;
}

export function BoardingConfirmationModal({
  isOpen,
  onClose,
  booking,
  onConfirm,
  isLoading = false
}: BoardingConfirmationModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleConfirm = async () => {
    if (!booking) return;

    try {
      setIsSubmitting(true);
      await onConfirm(booking.id);
      onClose();
      toast.success(`${booking.student_name} marked as boarded successfully`);
    } catch (error) {
      console.error('Error updating boarding status:', error);
      toast.error('Failed to update boarding status. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  };

  const formatCurrency = (amount: number | null) => {
    if (amount === null) return 'N/A';
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  if (!booking) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-600" />
            Confirm Boarding
          </DialogTitle>
          <DialogDescription>
            Please confirm that this student has physically boarded the bus.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Student Information */}
          <div className="bg-slate-50 rounded-lg p-4 space-y-3">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <User className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold text-slate-900">{booking.student_name}</h3>
                <p className="text-sm text-slate-600 font-mono">{booking.admission_number}</p>
              </div>
            </div>

            <Separator />

            {/* Travel Details */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Route className="w-4 h-4 text-slate-400" />
                <div>
                  <p className="text-slate-500">Route</p>
                  <p className="font-medium text-slate-900">{booking.bus_route}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-slate-400" />
                <div>
                  <p className="text-slate-500">Destination</p>
                  <p className="font-medium text-slate-900">{booking.destination}</p>
                </div>
              </div>
            </div>

            {/* Travel Dates */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-slate-400" />
                <div>
                  <p className="text-slate-500">Go Date</p>
                  <p className="font-medium text-slate-900">{formatDate(booking.go_date)}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-slate-400" />
                <div>
                  <p className="text-slate-500">Return Date</p>
                  <p className="font-medium text-slate-900">{formatDate(booking.return_date)}</p>
                </div>
              </div>
            </div>
            
          </div>

          {/* Warning Message */}
          <div className="flex items-start gap-3 p-3 bg-amber-50 border border-amber-200 rounded-lg">
            <AlertCircle className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="font-medium text-amber-800">Important</p>
              <p className="text-amber-700">
                Only mark as boarded when the student has physically entered the bus. 
                This action cannot be undone.
              </p>
            </div>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={isSubmitting}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            {isSubmitting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Confirming...
              </>
            ) : (
              <>
                <CheckCircle className="w-4 h-4 mr-2" />
                Confirm Boarded
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
